class Facebook::TypingIndicatorService
  attr_reader :channel, :recipient_id

  def initialize(channel, recipient_id)
    @channel = channel
    @recipient_id = recipient_id
  end

  def enable
    send_typing_indicator('typing_on')
  end

  def disable
    send_typing_indicator('typing_off')
  end

  def mark_seen
    send_typing_indicator('mark_seen')
  end

  private

  # Phương thức gửi typing indicator tối ưu theo Facebook API v22
  def send_typing_indicator(action)
    return false unless valid_channel?
    return false if recipient_id.blank?
    return false unless valid_action?(action)

    # Cấu hình chính xác theo Facebook Messenger Platform API
    # Endpoint: POST https://graph.facebook.com/v22.0/me/messages
    # Sender actions: typing_on, typing_off, mark_seen (phải lowercase theo tài liệu chín<PERSON> thức)
    typing_params = {
      recipient: { id: recipient_id },
      sender_action: action.downcase # Facebook API v22 yêu cầu lowercase: typing_on, typing_off, mark_seen
    }

    begin
      # Facebook::Messenger::Bot.deliver chỉ nhận delivery_params và page_id
      # Access token đượ<PERSON> l<PERSON> thông qua configuration provider (ChatwootFbProvider)
      result = Facebook::Messenger::Bot.deliver(
        typing_params,
        page_id: channel.page_id
      )

      parsed_result = result.is_a?(String) ? JSON.parse(result) : result

      if parsed_result && parsed_result['error'].present?
        error_code = parsed_result['error']['code'] || 'unknown'
        error_message = parsed_result['error']['message'] || 'Unknown error'
        Rails.logger.error "Facebook::TypingIndicatorService: Failed to send #{action} - Code #{error_code}: #{error_message}"
        return false
      end

      Rails.logger.info "Facebook::TypingIndicatorService: Successfully sent #{action} for recipient #{recipient_id}"
      true
    rescue => e
      Rails.logger.error "Facebook::TypingIndicatorService: Exception sending #{action}: #{e.message}"
      false
    end
  end

  def valid_action?(action)
    %w[typing_on typing_off mark_seen].include?(action.downcase)
  end

  def valid_channel?
    channel.present? && channel.is_a?(Channel::FacebookPage) && channel.page_access_token.present?
  end
end