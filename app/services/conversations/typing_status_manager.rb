class Conversations::TypingStatusManager
  include Events::Types

  attr_reader :conversation, :user, :params

  def initialize(conversation, user, params)
    @conversation = conversation
    @user = user
    @params = params
  end

  def toggle_typing_status
    case params[:typing_status]
    when 'on'
      trigger_typing_event(CONVERSATION_TYPING_ON)
    when 'off'
      trigger_typing_event(CONVERSATION_TYPING_OFF)
    end
  end

  private

  def trigger_typing_event(event)
    # Dispatch event cho UI (ActionCable)
    Rails.configuration.dispatcher.dispatch(
      event,
      Time.zone.now,
      conversation: @conversation,
      user: @user,
      is_private: params[:is_private] || false
    )

    # Chỉ gửi typing indicator đến platform khi là human agent (User)
    # Bỏ logic bot agent phức tạp, chỉ focus vào human agent
    if @user.is_a?(User) && @user.agent?
      send_platform_typing_indicator(event)
    end
  end

  def send_platform_typing_indicator(event)
    return unless @conversation.inbox.channel_type.in?(['Channel::FacebookPage', 'Channel::Instagram'])
    return if @conversation.contact.blank?

    begin
      typing_service = Bot::TypingService.new(conversation: @conversation)

      case event
      when CONVERSATION_TYPING_ON
        result = typing_service.enable_typing
        Rails.logger.info "TypingStatusManager: Human agent typing ON sent to #{@conversation.inbox.channel_type} - Success: #{result}"
      when CONVERSATION_TYPING_OFF
        result = typing_service.disable_typing
        Rails.logger.info "TypingStatusManager: Human agent typing OFF sent to #{@conversation.inbox.channel_type} - Success: #{result}"
      end
    rescue => e
      Rails.logger.error "TypingStatusManager: Error sending human agent typing to platform: #{e.message}"
    end
  end
end
