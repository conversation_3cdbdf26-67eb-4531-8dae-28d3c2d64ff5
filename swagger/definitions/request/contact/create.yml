type: object
required:
  - inbox_id
properties:
  inbox_id:
    type: number
  name:
    type: string
    description: name of the contact
  email:
    type: string
    description: email of the contact
  phone_number:
    type: string
    description: phone number of the contact
  avatar:
    type: string
    format: binary
    description: Send the form data with the avatar image binary or use the avatar_url
  avatar_url:
    type: string
    description: The url to a jpeg, png file for the contact avatar
  identifier:
    type: string
    description: A unique identifier for the contact in external system
  custom_attributes:
    type: object
    description: An object where you can store custom attributes for contact. example {"type":"customer", "age":30}
